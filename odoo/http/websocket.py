# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
WebSocket support for Odoo ASGI application.
This module provides modern WebSocket handling with proper authentication,
session management, and routing integration.
"""

import asyncio
import json
import logging
import time
import uuid
from typing import Dict, List, Optional, Callable, Any
from urllib.parse import parse_qs, urlparse

from odoo.tools import config
from odoo.http import request
from odoo.exceptions import AccessDenied

_logger = logging.getLogger(__name__)


class WebSocketConnection:
    """Represents a WebSocket connection with Odoo-specific features."""
    
    def __init__(self, websocket, scope, receive, send):
        self.websocket = websocket
        self.scope = scope
        self.receive = receive
        self.send = send
        self.connection_id = str(uuid.uuid4())
        self.created_at = time.time()
        self.last_ping = time.time()
        self.authenticated = False
        self.user_id = None
        self.session_id = None
        self.db_name = None
        self.channels = set()  # Subscribed channels
        self.closed = False
        
    @property
    def path(self):
        """Get the WebSocket path."""
        return self.scope.get('path', '/')
        
    @property
    def query_params(self):
        """Get query parameters from the WebSocket URL."""
        query_string = self.scope.get('query_string', b'').decode('utf-8')
        return parse_qs(query_string)
        
    @property
    def headers(self):
        """Get headers from the WebSocket handshake."""
        return dict(self.scope.get('headers', []))
        
    async def accept(self, subprotocol=None):
        """Accept the WebSocket connection."""
        message = {'type': 'websocket.accept'}
        if subprotocol:
            message['subprotocol'] = subprotocol
        await self.send(message)
        
    async def close(self, code=1000, reason=''):
        """Close the WebSocket connection."""
        if not self.closed:
            await self.send({
                'type': 'websocket.close',
                'code': code,
                'reason': reason
            })
            self.closed = True
            
    async def send_text(self, text):
        """Send text message to the WebSocket."""
        if not self.closed:
            await self.send({
                'type': 'websocket.send',
                'text': text
            })
            
    async def send_json(self, data):
        """Send JSON message to the WebSocket."""
        await self.send_text(json.dumps(data))
        
    async def receive_message(self):
        """Receive a message from the WebSocket."""
        message = await self.receive()
        return message
        
    def is_alive(self):
        """Check if the connection is still alive."""
        return not self.closed and (time.time() - self.last_ping) < config.get('websocket_keep_alive_timeout', 3600)


class WebSocketManager:
    """Manages WebSocket connections and routing."""
    
    def __init__(self):
        self.connections: Dict[str, WebSocketConnection] = {}
        self.handlers: Dict[str, Callable] = {}
        self.channels: Dict[str, set] = {}  # channel_name -> set of connection_ids
        
    def register_handler(self, path: str, handler: Callable):
        """Register a WebSocket handler for a specific path."""
        self.handlers[path] = handler
        _logger.info(f"Registered WebSocket handler for path: {path}")
        
    def add_connection(self, connection: WebSocketConnection):
        """Add a new WebSocket connection."""
        self.connections[connection.connection_id] = connection
        _logger.info(f"Added WebSocket connection: {connection.connection_id}")
        
    def remove_connection(self, connection_id: str):
        """Remove a WebSocket connection."""
        if connection_id in self.connections:
            connection = self.connections[connection_id]
            # Remove from all channels
            for channel in list(connection.channels):
                self.unsubscribe_from_channel(connection_id, channel)
            del self.connections[connection_id]
            _logger.info(f"Removed WebSocket connection: {connection_id}")
            
    def subscribe_to_channel(self, connection_id: str, channel: str):
        """Subscribe a connection to a channel."""
        if connection_id in self.connections:
            connection = self.connections[connection_id]
            connection.channels.add(channel)
            if channel not in self.channels:
                self.channels[channel] = set()
            self.channels[channel].add(connection_id)
            _logger.debug(f"Connection {connection_id} subscribed to channel: {channel}")
            
    def unsubscribe_from_channel(self, connection_id: str, channel: str):
        """Unsubscribe a connection from a channel."""
        if connection_id in self.connections:
            connection = self.connections[connection_id]
            connection.channels.discard(channel)
            if channel in self.channels:
                self.channels[channel].discard(connection_id)
                if not self.channels[channel]:
                    del self.channels[channel]
            _logger.debug(f"Connection {connection_id} unsubscribed from channel: {channel}")
            
    async def broadcast_to_channel(self, channel: str, message: dict):
        """Broadcast a message to all connections in a channel."""
        if channel in self.channels:
            disconnected = []
            for connection_id in self.channels[channel].copy():
                if connection_id in self.connections:
                    connection = self.connections[connection_id]
                    try:
                        await connection.send_json(message)
                    except Exception as e:
                        _logger.warning(f"Failed to send message to connection {connection_id}: {e}")
                        disconnected.append(connection_id)
                        
            # Clean up disconnected connections
            for connection_id in disconnected:
                self.remove_connection(connection_id)
                
    async def send_to_connection(self, connection_id: str, message: dict):
        """Send a message to a specific connection."""
        if connection_id in self.connections:
            connection = self.connections[connection_id]
            try:
                await connection.send_json(message)
            except Exception as e:
                _logger.warning(f"Failed to send message to connection {connection_id}: {e}")
                self.remove_connection(connection_id)
                
    def get_handler(self, path: str) -> Optional[Callable]:
        """Get the handler for a WebSocket path."""
        # Try exact match first
        if path in self.handlers:
            return self.handlers[path]
            
        # Try pattern matching (simple prefix matching for now)
        for handler_path, handler in self.handlers.items():
            if path.startswith(handler_path.rstrip('*')):
                return handler
                
        return None
        
    def cleanup_stale_connections(self):
        """Remove stale connections that haven't pinged recently."""
        stale_connections = []
        for connection_id, connection in self.connections.items():
            if not connection.is_alive():
                stale_connections.append(connection_id)
                
        for connection_id in stale_connections:
            self.remove_connection(connection_id)
            
        if stale_connections:
            _logger.info(f"Cleaned up {len(stale_connections)} stale WebSocket connections")


# Global WebSocket manager instance
websocket_manager = WebSocketManager()


async def authenticate_websocket(connection: WebSocketConnection) -> bool:
    """
    Authenticate a WebSocket connection using Odoo's session management.
    Returns True if authentication is successful.
    """
    try:
        # Get session ID from query parameters or headers
        session_id = None

        # Try query parameters first
        query_params = connection.query_params
        if 'session_id' in query_params:
            session_id = query_params['session_id'][0]

        # Try cookies from headers
        if not session_id:
            headers = connection.headers
            cookie_header = headers.get(b'cookie', b'').decode('utf-8')
            if cookie_header:
                for cookie in cookie_header.split(';'):
                    if 'session_id=' in cookie:
                        session_id = cookie.split('session_id=')[1].strip()
                        break

        if not session_id:
            _logger.warning("No session ID found in WebSocket connection")
            return False

        # Get database name from query parameters
        db_name = None
        if 'db' in query_params:
            db_name = query_params['db'][0]
        elif 'database' in query_params:
            db_name = query_params['database'][0]

        # Validate session with Odoo's session store
        if await _validate_odoo_session(session_id, db_name, connection):
            connection.session_id = session_id
            connection.db_name = db_name
            connection.authenticated = True
            _logger.info(f"WebSocket connection {connection.connection_id} authenticated with session {session_id}")
            return True
        else:
            _logger.warning(f"Invalid session {session_id} for WebSocket connection")
            return False

    except Exception as e:
        _logger.error(f"WebSocket authentication failed: {e}")
        return False


async def _validate_odoo_session(session_id: str, db_name: Optional[str], connection: WebSocketConnection) -> bool:
    """
    Validate session with Odoo's session store and extract user information.
    """
    try:
        import odoo
        from odoo.http import root
        from odoo.service import db

        # Check if database exists
        if db_name and not db.exp_db_exist(db_name):
            _logger.warning(f"Database {db_name} does not exist")
            return False

        # Get session from Odoo's session store
        session = root.session_store.get(session_id)
        if not session:
            _logger.warning(f"Session {session_id} not found in session store")
            return False

        # Check if session is valid and not expired
        if session.should_rotate or not session.uid:
            _logger.warning(f"Session {session_id} is expired or invalid")
            return False

        # Validate database matches
        if db_name and session.db != db_name:
            _logger.warning(f"Session database {session.db} does not match requested {db_name}")
            return False

        # Set connection user information
        connection.user_id = session.uid
        connection.db_name = session.db or db_name

        # Additional validation: check if user exists and is active
        if connection.db_name:
            try:
                registry = odoo.modules.registry.Registry(connection.db_name)
                with registry.cursor() as cr:
                    env = odoo.api.Environment(cr, session.uid, session.context or {})
                    user = env['res.users'].browse(session.uid)
                    if not user.exists() or not user.active:
                        _logger.warning(f"User {session.uid} does not exist or is inactive")
                        return False

                    # Store additional user info
                    connection.user_login = user.login
                    connection.user_name = user.name

            except Exception as e:
                _logger.error(f"Error validating user {session.uid}: {e}")
                return False

        return True

    except Exception as e:
        _logger.error(f"Session validation error: {e}")
        return False


def check_websocket_auth(auth_type: str, connection: WebSocketConnection) -> bool:
    """
    Check if connection meets authentication requirements.

    :param auth_type: Authentication type ('public', 'user', 'admin')
    :param connection: WebSocket connection
    :return: True if authentication check passes
    """
    if auth_type == 'public':
        return True
    elif auth_type == 'user':
        return connection.authenticated and connection.user_id
    elif auth_type == 'admin':
        if not connection.authenticated or not connection.user_id:
            return False

        # Check if user is admin
        try:
            import odoo
            if connection.db_name:
                registry = odoo.modules.registry.Registry(connection.db_name)
                with registry.cursor() as cr:
                    env = odoo.api.Environment(cr, connection.user_id, {})
                    user = env['res.users'].browse(connection.user_id)
                    return user.has_group('base.group_system')
        except Exception as e:
            _logger.error(f"Error checking admin privileges: {e}")
            return False

    return False


def websocket_route(path: str):
    """Decorator to register WebSocket handlers."""
    def decorator(handler):
        websocket_manager.register_handler(path, handler)
        return handler
    return decorator


async def handle_websocket_connection(scope, receive, send):
    """
    Main WebSocket connection handler for ASGI.
    This function is called by the ASGI application for WebSocket connections.
    """
    connection = WebSocketConnection(None, scope, receive, send)

    try:
        # Accept the connection
        await connection.accept()

        # Add to manager
        websocket_manager.add_connection(connection)

        # Authenticate the connection
        if not await authenticate_websocket(connection):
            await connection.close(code=4001, reason="Authentication failed")
            return

        # Use the new routing system
        from .websocket_controllers import websocket_dispatcher
        await websocket_dispatcher.dispatch(connection)

    except Exception as e:
        _logger.error(f"WebSocket connection error: {e}")
    finally:
        # Clean up
        websocket_manager.remove_connection(connection.connection_id)


async def default_websocket_handler(connection: WebSocketConnection):
    """Default WebSocket handler that echoes messages."""
    try:
        await connection.send_json({
            'type': 'connection_established',
            'connection_id': connection.connection_id,
            'message': 'WebSocket connection established'
        })

        while not connection.closed:
            message = await connection.receive_message()

            if message['type'] == 'websocket.receive':
                if 'text' in message:
                    try:
                        data = json.loads(message['text'])

                        # Handle ping messages
                        if data.get('type') == 'ping':
                            connection.last_ping = time.time()
                            await connection.send_json({
                                'type': 'pong',
                                'timestamp': time.time()
                            })
                        else:
                            # Echo the message back
                            await connection.send_json({
                                'type': 'echo',
                                'data': data,
                                'timestamp': time.time()
                            })
                    except json.JSONDecodeError:
                        await connection.send_json({
                            'type': 'error',
                            'message': 'Invalid JSON format'
                        })

            elif message['type'] == 'websocket.disconnect':
                break

    except Exception as e:
        _logger.error(f"Default WebSocket handler error: {e}")
    finally:
        if not connection.closed:
            await connection.close()


# Periodic cleanup task
async def cleanup_websocket_connections():
    """Periodic task to clean up stale WebSocket connections."""
    while True:
        try:
            websocket_manager.cleanup_stale_connections()
            await asyncio.sleep(60)  # Run every minute
        except Exception as e:
            _logger.error(f"WebSocket cleanup error: {e}")
            await asyncio.sleep(60)
