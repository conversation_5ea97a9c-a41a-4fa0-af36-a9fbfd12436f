# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
WebSocket controllers for Odoo.
This module provides WebSocket controller classes and routing decorators
similar to HTTP controllers but for WebSocket connections.
"""

import asyncio
import inspect
import logging
import re
from typing import Dict, List, Optional, Callable, Any, Pattern
from urllib.parse import parse_qs

from odoo.tools import unique
from odoo.tools.func import filter_kwargs

_logger = logging.getLogger(__name__)


class WebSocketRoute:
    """Represents a WebSocket route configuration."""
    
    def __init__(self, pattern: str, handler: Callable, auth: str = 'user', 
                 cors: bool = False, csrf: bool = True, **kwargs):
        self.pattern = pattern
        self.handler = handler
        self.auth = auth
        self.cors = cors
        self.csrf = csrf
        self.kwargs = kwargs
        
        # Compile regex pattern for URL matching
        self._regex = self._compile_pattern(pattern)
        
    def _compile_pattern(self, pattern: str) -> Pattern:
        """Compile URL pattern to regex."""
        # Convert URL pattern to regex
        # Handle path parameters like /ws/<string:channel>
        regex_pattern = pattern

        # Replace parameter patterns
        regex_pattern = re.sub(r'<string:(\w+)>', r'(?P<\1>[^/]+)', regex_pattern)
        regex_pattern = re.sub(r'<int:(\w+)>', r'(?P<\1>\\d+)', regex_pattern)
        regex_pattern = re.sub(r'<path:(\w+)>', r'(?P<\1>.*)', regex_pattern)

        # Escape special regex characters except our parameters
        regex_pattern = regex_pattern.replace('/', r'\/')

        # Ensure exact match
        regex_pattern = f'^{regex_pattern}$'

        return re.compile(regex_pattern)
        
    def match(self, path: str) -> Optional[Dict[str, str]]:
        """Check if path matches this route and return parameters."""
        match = self._regex.match(path)
        if match:
            return match.groupdict()
        return None


class WebSocketController:
    """
    Base class for WebSocket controllers.
    
    WebSocket controllers handle real-time communication over WebSocket connections.
    They provide methods to handle connection events, messages, and disconnections.
    
    Example:
        class ChatController(WebSocketController):
            @websocket_route('/ws/chat/<string:room>')
            async def chat_room(self, connection, room):
                await connection.subscribe_to_channel(f'chat_{room}')
                async for message in connection.messages():
                    await self.broadcast_to_room(room, message)
    """
    
    def __init__(self):
        self.routes = []
        self._setup_routes()
        
    def _setup_routes(self):
        """Setup routes from decorated methods."""
        for attr_name in dir(self):
            attr = getattr(self, attr_name)
            if hasattr(attr, '_websocket_route'):
                route_info = attr._websocket_route
                route = WebSocketRoute(
                    pattern=route_info['pattern'],
                    handler=attr,
                    **route_info.get('kwargs', {})
                )
                self.routes.append(route)
                
    async def handle_connection(self, connection, path: str):
        """Handle incoming WebSocket connection."""
        for route in self.routes:
            params = route.match(path)
            if params is not None:
                # Check authentication requirements
                from .websocket import check_websocket_auth
                if not check_websocket_auth(route.auth, connection):
                    await connection.close(code=4001, reason="Authentication failed")
                    return True

                try:
                    # Call the handler with connection and path parameters
                    if inspect.iscoroutinefunction(route.handler):
                        await route.handler(connection, **params)
                    else:
                        route.handler(connection, **params)
                    return True
                except Exception as e:
                    _logger.error(f"WebSocket route handler error: {e}")
                    await connection.close(code=1011, reason="Handler error")
                    return True
        return False
        
    async def on_connect(self, connection):
        """Called when a WebSocket connection is established."""
        pass
        
    async def on_disconnect(self, connection):
        """Called when a WebSocket connection is closed."""
        pass
        
    async def on_message(self, connection, message):
        """Called when a message is received."""
        pass


class WebSocketRegistry:
    """Registry for WebSocket controllers and routes."""
    
    def __init__(self):
        self.controllers: List[WebSocketController] = []
        self.routes: List[WebSocketRoute] = []
        
    def register_controller(self, controller: WebSocketController):
        """Register a WebSocket controller."""
        self.controllers.append(controller)
        self.routes.extend(controller.routes)
        _logger.info(f"Registered WebSocket controller: {controller.__class__.__name__}")
        
    def find_handler(self, path: str) -> Optional[tuple]:
        """Find handler for a WebSocket path."""
        for route in self.routes:
            params = route.match(path)
            if params is not None:
                return route.handler, params
        return None
        
    def get_all_routes(self) -> List[WebSocketRoute]:
        """Get all registered routes."""
        return self.routes.copy()


# Global WebSocket registry
websocket_registry = WebSocketRegistry()


def websocket_route(pattern: str, auth: str = 'user', cors: bool = False, 
                   csrf: bool = True, **kwargs):
    """
    Decorator to mark methods as WebSocket route handlers.
    
    :param str pattern: URL pattern for the WebSocket route
    :param str auth: Authentication requirement ('public', 'user', 'admin')
    :param bool cors: Enable CORS support
    :param bool csrf: Enable CSRF protection
    """
    def decorator(func):
        func._websocket_route = {
            'pattern': pattern,
            'kwargs': {
                'auth': auth,
                'cors': cors,
                'csrf': csrf,
                **kwargs
            }
        }
        return func
    return decorator


def register_websocket_controller(controller_class):
    """Register a WebSocket controller class."""
    controller = controller_class()
    websocket_registry.register_controller(controller)
    return controller_class


async def route_websocket_connection(connection):
    """Route a WebSocket connection to the appropriate handler."""
    path = connection.path
    
    # Try to find a handler in the registry
    handler_info = websocket_registry.find_handler(path)
    if handler_info:
        handler, params = handler_info
        try:
            await handler(connection, **params)
            return True
        except Exception as e:
            _logger.error(f"WebSocket handler error for path {path}: {e}")
            await connection.close(code=1011, reason="Handler error")
            return True
            
    return False


def generate_websocket_routing_rules(modules, nodb_only=False):
    """
    Generate WebSocket routing rules from installed modules.
    Similar to HTTP routing but for WebSocket endpoints.
    """
    rules = []
    
    for module_name in modules:
        try:
            # Import module and scan for WebSocket controllers
            module = __import__(f'odoo.addons.{module_name}.websocket_controllers', fromlist=[''])
            
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (inspect.isclass(attr) and 
                    issubclass(attr, WebSocketController) and 
                    attr is not WebSocketController):
                    
                    # Register the controller
                    register_websocket_controller(attr)
                    
        except ImportError:
            # Module doesn't exist or doesn't have WebSocket controllers
            continue
    
    return websocket_registry.get_all_routes()


class WebSocketDispatcher:
    """Dispatcher for WebSocket connections."""
    
    def __init__(self):
        self.registry = websocket_registry
        
    async def dispatch(self, connection):
        """Dispatch a WebSocket connection to the appropriate handler."""
        try:
            # Try registered controllers first
            for controller in self.registry.controllers:
                if await controller.handle_connection(connection, connection.path):
                    return
                    
            # If no controller handled it, try route-based dispatch
            if await route_websocket_connection(connection):
                return
                
            # No handler found, use default behavior
            _logger.warning(f"No WebSocket handler found for path: {connection.path}")
            await connection.send_json({
                'type': 'error',
                'message': f'No handler found for path: {connection.path}'
            })
            await connection.close(code=4004, reason="No handler found")
            
        except Exception as e:
            _logger.error(f"WebSocket dispatch error: {e}")
            await connection.close(code=1011, reason="Dispatch error")


# Global WebSocket dispatcher
websocket_dispatcher = WebSocketDispatcher()
