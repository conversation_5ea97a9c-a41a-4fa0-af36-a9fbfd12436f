# Part of Odoo. See LICENSE file for full copyright and licensing details.

import asyncio
import logging
import os
import threading
import time
from urllib.parse import urlparse

import werkzeug.routing
from werkzeug.middleware.proxy_fix import ProxyFix
from werkzeug.exceptions import HTTPException
from starlette.applications import Starlette
from starlette.requests import Request as StarletteRequest
from starlette.responses import Response as StarletteResponse
from starlette.middleware import Middleware
from starlette.middleware.base import BaseHTTPMiddleware

from odoo.modules.module import get_manifest
from odoo.tools import lazy_property, file_path
from odoo.tools.misc import submap
from os.path import join as opj
from odoo.exceptions import UserError, AccessError

from .utils import ROUTING_KEYS, RegistryError, SessionExpiredException
from .controllers import _generate_routing_rules
from .session import FilesystemSessionStore, Session
from .request_response import HTTPRequest, Request, _request_stack
from .dispatchers import HttpDispatcher

try:
    import geoip2.database
    import maxminddb
except ImportError:
    geoip2 = None

_logger = logging.getLogger(__name__)


class OdooASGIMiddleware(BaseHTTPMiddleware):
    """ASGI middleware to handle Odoo-specific request processing."""
    
    def __init__(self, app, odoo_app):
        super().__init__(app)
        self.odoo_app = odoo_app

    async def dispatch(self, request, call_next):
        """Process request through Odoo's async request handling."""
        # Convert Starlette request to Odoo's internal format
        environ = await self._starlette_to_environ(request)
        
        # Process through Odoo's async application
        response_data = await self.odoo_app.async_call(environ)
        
        # Convert back to Starlette response
        return await self._create_starlette_response(response_data)

    async def _starlette_to_environ(self, request: StarletteRequest):
        """Convert Starlette request to WSGI-like environ dict."""
        environ = {
            'REQUEST_METHOD': request.method,
            'PATH_INFO': request.url.path,
            'QUERY_STRING': str(request.url.query),
            'CONTENT_TYPE': request.headers.get('content-type', ''),
            'CONTENT_LENGTH': request.headers.get('content-length', ''),
            'SERVER_NAME': request.url.hostname or 'localhost',
            'SERVER_PORT': str(request.url.port or 80),
            'wsgi.url_scheme': request.url.scheme,
            'HTTP_HOST': request.headers.get('host', ''),
            'REMOTE_ADDR': request.client.host if request.client else '',
        }
        
        # Add HTTP headers
        for name, value in request.headers.items():
            key = f'HTTP_{name.upper().replace("-", "_")}'
            environ[key] = value
        
        # Add request body
        if request.method in ('POST', 'PUT', 'PATCH'):
            body = await request.body()
            environ['wsgi.input'] = body
        
        return environ

    async def _create_starlette_response(self, response_data):
        """Create Starlette response from Odoo response data."""
        status_code, headers, content = response_data
        return StarletteResponse(
            content=content,
            status_code=status_code,
            headers=dict(headers)
        )


class AsyncApplication:
    """Odoo ASGI application - async version of the WSGI Application."""
    
    def __init__(self):
        self._starlette_app = None
        self._setup_starlette()

    def _setup_starlette(self):
        """Setup the Starlette ASGI application."""
        from starlette.routing import Route

        # Create a catch-all route that handles all requests
        routes = [
            Route("/{path:path}", self._handle_request, methods=["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"])
        ]

        self._starlette_app = Starlette(
            routes=routes,
            debug=False  # Set based on Odoo config
        )

    async def _handle_request(self, request):
        """Handle incoming Starlette request."""
        from .async_request_response import AsyncHTTPRequest, AsyncRequest, AsyncResponse, set_async_request

        try:
            # Create async HTTP request wrapper
            async_httprequest = AsyncHTTPRequest(request)
            async_request = AsyncRequest(async_httprequest)

            # Set the request in the async context
            set_async_request(async_request)

            # Initialize the request
            await async_request._async_post_init()

            # Determine response based on request
            if self.get_static_file(async_httprequest.path):
                response = await async_request._async_serve_static()
            elif async_request.db:
                response = await async_request._async_serve_db()
            else:
                response = await async_request._async_serve_nodb()

            # Convert to Starlette response
            return response.to_starlette_response()

        except Exception as e:
            _logger.error("Error handling request", exc_info=True)
            return AsyncResponse("Internal Server Error", status=500).to_starlette_response()

    @lazy_property
    def statics(self):
        """Map module names to their absolute static path on the file system."""
        import odoo.addons
        mod2path = {}
        for addons_path in odoo.addons.__path__:
            for module in os.listdir(addons_path):
                manifest = get_manifest(module)
                static_path = opj(addons_path, module, 'static')
                if (manifest
                        and (manifest['installable'] or manifest['assets'])
                        and os.path.isdir(static_path)):
                    mod2path[module] = static_path
        return mod2path

    def get_static_file(self, url, host=''):
        """Get the full-path of the file if the url resolves to a local static file."""
        netloc, path = urlparse(url)[1:3]
        try:
            path_netloc, module, static, resource = path.split('/', 3)
        except ValueError:
            return None

        if ((netloc and netloc != host) or (path_netloc and path_netloc != host)):
            return None

        if (module not in self.statics or static != 'static' or not resource):
            return None

        try:
            return file_path(f'{module}/static/{resource}')
        except FileNotFoundError:
            return None

    @lazy_property
    def nodb_routing_map(self):
        import odoo.conf
        nodb_routing_map = werkzeug.routing.Map(strict_slashes=False, converters=None)
        for url, endpoint in _generate_routing_rules([''] + odoo.conf.server_wide_modules, nodb_only=True):
            routing = submap(endpoint.routing, ROUTING_KEYS)
            if routing['methods'] is not None and 'OPTIONS' not in routing['methods']:
                routing['methods'] = [*routing['methods'], 'OPTIONS']
            rule = werkzeug.routing.Rule(url, endpoint=endpoint, **routing)
            rule.merge_slashes = False
            nodb_routing_map.add(rule)
        return nodb_routing_map

    @lazy_property
    def session_store(self):
        import odoo.tools.config
        path = odoo.tools.config.session_dir
        _logger.debug('HTTP sessions stored in: %s', path)
        return FilesystemSessionStore(path, session_class=Session, renew_missing=True)

    def get_db_router(self, db):
        """Get database router for the given database."""
        if not db:
            return self.nodb_routing_map
        # This will need to be updated for async operation
        from .request_response import request
        return request.env['ir.http'].routing_map()

    def set_csp(self, response):
        """Set Content Security Policy headers on the response."""
        if hasattr(response, 'headers'):
            headers = response.headers
            headers['X-Content-Type-Options'] = 'nosniff'

            if 'Content-Security-Policy' in headers:
                return

            if not headers.get('Content-Type', '').startswith('image/'):
                return

            headers['Content-Security-Policy'] = "default-src 'none'"

    async def async_call(self, environ):
        """
        ASGI application entry point - async version of WSGI __call__.
        
        :param dict environ: container for CGI environment variables
        :return: tuple of (status_code, headers, content)
        """
        current_thread = threading.current_thread()
        current_thread.query_count = 0
        current_thread.query_time = 0
        current_thread.perf_t0 = time.time()
        current_thread.cursor_mode = None
        
        if hasattr(current_thread, 'dbname'):
            del current_thread.dbname
        if hasattr(current_thread, 'uid'):
            del current_thread.uid

        import odoo.tools.config
        if odoo.tools.config['proxy_mode'] and environ.get("HTTP_X_FORWARDED_HOST"):
            # Handle proxy fix for ASGI
            pass  # TODO: Implement proxy fix for ASGI

        try:
            # Create async HTTP request wrapper
            async_httprequest = await self._create_async_httprequest(environ)
            request = await self._create_async_request(async_httprequest)
            _request_stack.push(request)

            try:
                await request._async_post_init()
                current_thread.url = async_httprequest.url

                if self.get_static_file(async_httprequest.path):
                    response = await request._async_serve_static()
                elif request.db:
                    try:
                        # TODO: Implement async profiler context manager
                        response = await request._async_serve_db()
                    except RegistryError as e:
                        _logger.warning("Database or registry unusable, trying without", exc_info=e.__cause__)
                        request.db = None
                        request.session.logout()
                        response = await request._async_serve_nodb()
                else:
                    response = await request._async_serve_nodb()
                
                return await self._response_to_asgi_format(response)

            except Exception as exc:
                if isinstance(exc, HTTPException) and exc.code is None:
                    response = exc.get_response()
                    HttpDispatcher(request).post_dispatch(response)
                    return await self._response_to_asgi_format(response)

                # Log the error
                if hasattr(exc, 'loglevel'):
                    _logger.log(exc.loglevel, exc, exc_info=getattr(exc, 'exc_info', None))
                elif isinstance(exc, HTTPException):
                    pass
                elif isinstance(exc, SessionExpiredException):
                    _logger.info(exc)
                elif isinstance(exc, (UserError, AccessError)):
                    _logger.warning(exc)
                else:
                    _logger.error("Exception during request handling.", exc_info=True)

                # Handle error response
                if not hasattr(exc, 'error_response'):
                    exc.error_response = request.dispatcher.handle_error(exc)

                return await self._response_to_asgi_format(exc.error_response)

            finally:
                _request_stack.pop()

        except Exception as e:
            _logger.error("Critical error in ASGI application", exc_info=True)
            return (500, [], b"Internal Server Error")

    async def _create_async_httprequest(self, environ):
        """Create async HTTP request from environ."""
        # TODO: Implement async version of HTTPRequest
        return HTTPRequest(environ)

    async def _create_async_request(self, httprequest):
        """Create async request from HTTP request."""
        from .async_request_response import AsyncRequest, AsyncHTTPRequest
        # Create AsyncHTTPRequest wrapper
        async_httprequest = AsyncHTTPRequest(httprequest)
        # Create AsyncRequest
        return AsyncRequest(async_httprequest)

    async def _response_to_asgi_format(self, response):
        """Convert Odoo response to ASGI format."""
        # TODO: Implement response conversion
        return (200, [], b"OK")

    async def __call__(self, scope, receive, send):
        """ASGI application interface."""
        if scope['type'] == 'http':
            await self._starlette_app(scope, receive, send)
        elif scope['type'] == 'lifespan':
            # Handle lifespan events
            message = await receive()
            if message['type'] == 'lifespan.startup':
                # Perform startup tasks
                await self._handle_startup()
                await send({'type': 'lifespan.startup.complete'})
            elif message['type'] == 'lifespan.shutdown':
                # Perform shutdown tasks
                await self._handle_shutdown()
                await send({'type': 'lifespan.shutdown.complete'})
        elif scope['type'] == 'websocket':
            # Handle websocket connections with modern handler
            from .websocket import handle_websocket_connection
            await handle_websocket_connection(scope, receive, send)
        else:
            # Unknown scope type
            pass

    async def _handle_startup(self):
        """Handle application startup tasks."""
        import asyncio
        from .websocket import cleanup_websocket_connections
        from .websocket_bus import start_bus_integration

        # Start WebSocket cleanup task
        asyncio.create_task(cleanup_websocket_connections())

        # Start bus integration
        asyncio.create_task(start_bus_integration())

        _logger.info("ASGI application startup complete with WebSocket and Bus support")

    async def _handle_shutdown(self):
        """Handle application shutdown tasks."""
        _logger.info("ASGI application shutdown initiated")


# Global ASGI application instance
async_root = AsyncApplication()
