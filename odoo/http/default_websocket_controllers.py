# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Default WebSocket controllers for Odoo.
This module provides basic WebSocket endpoints for testing and demonstration.
"""

import json
import logging
import time
from typing import Dict, Any

from .websocket_controllers import WebSocketController, websocket_route, register_websocket_controller
from .websocket import websocket_manager

_logger = logging.getLogger(__name__)


@register_websocket_controller
class DefaultWebSocketController(WebSocketController):
    """Default WebSocket controller with basic endpoints."""
    
    @websocket_route('/ws/echo', auth='public')
    async def echo_handler(self, connection):
        """Echo WebSocket endpoint for testing."""
        await connection.send_json({
            'type': 'connection_established',
            'endpoint': 'echo',
            'connection_id': connection.connection_id,
            'message': 'Echo WebSocket endpoint ready'
        })
        
        try:
            while not connection.closed:
                message = await connection.receive_message()
                
                if message['type'] == 'websocket.receive':
                    if 'text' in message:
                        try:
                            data = json.loads(message['text'])
                            
                            # Handle ping messages
                            if data.get('type') == 'ping':
                                connection.last_ping = time.time()
                                await connection.send_json({
                                    'type': 'pong',
                                    'timestamp': time.time()
                                })
                            else:
                                # Echo the message back
                                await connection.send_json({
                                    'type': 'echo',
                                    'data': data,
                                    'timestamp': time.time(),
                                    'connection_id': connection.connection_id
                                })
                        except json.JSONDecodeError:
                            await connection.send_json({
                                'type': 'error',
                                'message': 'Invalid JSON format'
                            })
                            
                elif message['type'] == 'websocket.disconnect':
                    break
                    
        except Exception as e:
            _logger.error(f"Echo handler error: {e}")
        finally:
            if not connection.closed:
                await connection.close()
    
    @websocket_route('/ws/status', auth='public')
    async def status_handler(self, connection):
        """Status WebSocket endpoint."""
        await connection.send_json({
            'type': 'status',
            'server_time': time.time(),
            'connection_id': connection.connection_id,
            'total_connections': len(websocket_manager.connections),
            'total_channels': len(websocket_manager.channels),
            'message': 'WebSocket server is running'
        })
        await connection.close()
    
    @websocket_route('/ws/channel/<string:channel_name>', auth='user')
    async def channel_handler(self, connection, channel_name):
        """Channel-based WebSocket endpoint."""
        # Subscribe to the channel
        websocket_manager.subscribe_to_channel(connection.connection_id, channel_name)
        
        await connection.send_json({
            'type': 'channel_joined',
            'channel': channel_name,
            'connection_id': connection.connection_id,
            'message': f'Joined channel: {channel_name}'
        })
        
        try:
            while not connection.closed:
                message = await connection.receive_message()
                
                if message['type'] == 'websocket.receive':
                    if 'text' in message:
                        try:
                            data = json.loads(message['text'])
                            
                            # Handle different message types
                            if data.get('type') == 'ping':
                                connection.last_ping = time.time()
                                await connection.send_json({
                                    'type': 'pong',
                                    'timestamp': time.time()
                                })
                            elif data.get('type') == 'broadcast':
                                # Broadcast message to all connections in the channel
                                broadcast_message = {
                                    'type': 'channel_message',
                                    'channel': channel_name,
                                    'from_connection': connection.connection_id,
                                    'data': data.get('data'),
                                    'timestamp': time.time()
                                }
                                await websocket_manager.broadcast_to_channel(channel_name, broadcast_message)
                            elif data.get('type') == 'leave':
                                # Leave the channel
                                websocket_manager.unsubscribe_from_channel(connection.connection_id, channel_name)
                                await connection.send_json({
                                    'type': 'channel_left',
                                    'channel': channel_name,
                                    'message': f'Left channel: {channel_name}'
                                })
                                break
                            else:
                                # Send message only to this connection
                                await connection.send_json({
                                    'type': 'channel_echo',
                                    'channel': channel_name,
                                    'data': data,
                                    'timestamp': time.time()
                                })
                                
                        except json.JSONDecodeError:
                            await connection.send_json({
                                'type': 'error',
                                'message': 'Invalid JSON format'
                            })
                            
                elif message['type'] == 'websocket.disconnect':
                    break
                    
        except Exception as e:
            _logger.error(f"Channel handler error: {e}")
        finally:
            # Ensure we're unsubscribed from the channel
            websocket_manager.unsubscribe_from_channel(connection.connection_id, channel_name)
            if not connection.closed:
                await connection.close()
    
    @websocket_route('/ws/admin/stats', auth='admin')
    async def admin_stats_handler(self, connection):
        """Admin statistics WebSocket endpoint."""
        if not connection.authenticated:
            await connection.close(code=4001, reason="Authentication required")
            return
            
        # Send detailed statistics
        stats = {
            'type': 'admin_stats',
            'timestamp': time.time(),
            'connections': {
                'total': len(websocket_manager.connections),
                'details': [
                    {
                        'id': conn.connection_id,
                        'path': conn.path,
                        'created_at': conn.created_at,
                        'last_ping': conn.last_ping,
                        'authenticated': conn.authenticated,
                        'user_id': conn.user_id,
                        'db_name': conn.db_name,
                        'channels': list(conn.channels)
                    }
                    for conn in websocket_manager.connections.values()
                ]
            },
            'channels': {
                'total': len(websocket_manager.channels),
                'details': {
                    channel: len(connections)
                    for channel, connections in websocket_manager.channels.items()
                }
            }
        }
        
        await connection.send_json(stats)
        await connection.close()


@register_websocket_controller  
class NotificationWebSocketController(WebSocketController):
    """WebSocket controller for real-time notifications."""
    
    @websocket_route('/ws/notifications', auth='user')
    async def notifications_handler(self, connection):
        """Handle real-time notifications for authenticated users."""
        if not connection.authenticated:
            await connection.close(code=4001, reason="Authentication required")
            return
            
        # Subscribe to user-specific notification channel
        user_channel = f"user_{connection.user_id or 'anonymous'}"
        websocket_manager.subscribe_to_channel(connection.connection_id, user_channel)
        
        # Also subscribe to broadcast channel
        websocket_manager.subscribe_to_channel(connection.connection_id, "broadcast")
        
        await connection.send_json({
            'type': 'notifications_ready',
            'user_channel': user_channel,
            'connection_id': connection.connection_id,
            'message': 'Notification system ready'
        })
        
        try:
            while not connection.closed:
                message = await connection.receive_message()
                
                if message['type'] == 'websocket.receive':
                    if 'text' in message:
                        try:
                            data = json.loads(message['text'])
                            
                            if data.get('type') == 'ping':
                                connection.last_ping = time.time()
                                await connection.send_json({
                                    'type': 'pong',
                                    'timestamp': time.time()
                                })
                            elif data.get('type') == 'subscribe':
                                # Subscribe to additional channels
                                channel = data.get('channel')
                                if channel:
                                    websocket_manager.subscribe_to_channel(connection.connection_id, channel)
                                    await connection.send_json({
                                        'type': 'subscribed',
                                        'channel': channel
                                    })
                            elif data.get('type') == 'unsubscribe':
                                # Unsubscribe from channels
                                channel = data.get('channel')
                                if channel:
                                    websocket_manager.unsubscribe_from_channel(connection.connection_id, channel)
                                    await connection.send_json({
                                        'type': 'unsubscribed',
                                        'channel': channel
                                    })
                                    
                        except json.JSONDecodeError:
                            await connection.send_json({
                                'type': 'error',
                                'message': 'Invalid JSON format'
                            })
                            
                elif message['type'] == 'websocket.disconnect':
                    break
                    
        except Exception as e:
            _logger.error(f"Notifications handler error: {e}")
        finally:
            if not connection.closed:
                await connection.close()
