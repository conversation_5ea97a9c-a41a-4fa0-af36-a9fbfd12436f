# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
WebSocket testing utilities and test cases for Odoo.
"""

import asyncio
import json
import logging
import unittest
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, Any, List

import odoo
from odoo.tests.common import TransactionCase, HttpCase
from odoo.http.websocket import WebSocketConnection, WebSocketManager, websocket_manager
from odoo.http.websocket_bus import bus_service, WebSocketBusService
from odoo.http.websocket_controllers import WebSocketController, websocket_route

_logger = logging.getLogger(__name__)


class MockWebSocketConnection:
    """Mock WebSocket connection for testing."""
    
    def __init__(self, connection_id: str = "test_conn", user_id: int = 1, db_name: str = "test_db"):
        self.connection_id = connection_id
        self.user_id = user_id
        self.db_name = db_name
        self.authenticated = True
        self.closed = False
        self.channels = set()
        self.sent_messages = []
        self.received_messages = []
        
    async def send_json(self, data: Dict[str, Any]):
        """Mock send_json method."""
        self.sent_messages.append(data)
        
    async def close(self, code: int = 1000, reason: str = ""):
        """Mock close method."""
        self.closed = True
        
    def add_received_message(self, message: Dict[str, Any]):
        """Add a message to the received queue for testing."""
        self.received_messages.append({
            'type': 'websocket.receive',
            'text': json.dumps(message)
        })
        
    async def receive_message(self):
        """Mock receive_message method."""
        if self.received_messages:
            return self.received_messages.pop(0)
        else:
            # Simulate disconnect if no more messages
            return {'type': 'websocket.disconnect'}


class WebSocketTestCase(TransactionCase):
    """Base test case for WebSocket functionality."""
    
    def setUp(self):
        super().setUp()
        self.websocket_manager = WebSocketManager()
        self.bus_service = WebSocketBusService()
        
    def create_mock_connection(self, connection_id: str = None, user_id: int = None) -> MockWebSocketConnection:
        """Create a mock WebSocket connection."""
        if connection_id is None:
            connection_id = f"test_conn_{len(self.websocket_manager.connections)}"
        if user_id is None:
            user_id = self.env.user.id
            
        connection = MockWebSocketConnection(
            connection_id=connection_id,
            user_id=user_id,
            db_name=self.env.cr.dbname
        )
        
        self.websocket_manager.add_connection(connection)
        return connection
        
    def tearDown(self):
        # Clean up connections
        for connection_id in list(self.websocket_manager.connections.keys()):
            self.websocket_manager.remove_connection(connection_id)
        super().tearDown()


class TestWebSocketManager(WebSocketTestCase):
    """Test WebSocket manager functionality."""
    
    def test_add_remove_connection(self):
        """Test adding and removing connections."""
        connection = self.create_mock_connection("test_1")
        
        self.assertIn("test_1", self.websocket_manager.connections)
        
        self.websocket_manager.remove_connection("test_1")
        self.assertNotIn("test_1", self.websocket_manager.connections)
        
    def test_channel_subscription(self):
        """Test channel subscription and unsubscription."""
        connection = self.create_mock_connection("test_1")
        
        # Subscribe to channel
        self.websocket_manager.subscribe_to_channel("test_1", "test_channel")
        
        self.assertIn("test_channel", connection.channels)
        self.assertIn("test_1", self.websocket_manager.channels["test_channel"])
        
        # Unsubscribe from channel
        self.websocket_manager.unsubscribe_from_channel("test_1", "test_channel")
        
        self.assertNotIn("test_channel", connection.channels)
        self.assertNotIn("test_channel", self.websocket_manager.channels)
        
    async def test_broadcast_to_channel(self):
        """Test broadcasting messages to a channel."""
        connection1 = self.create_mock_connection("test_1")
        connection2 = self.create_mock_connection("test_2")
        
        # Subscribe both connections to the same channel
        self.websocket_manager.subscribe_to_channel("test_1", "broadcast_test")
        self.websocket_manager.subscribe_to_channel("test_2", "broadcast_test")
        
        # Broadcast a message
        test_message = {"type": "test", "data": "hello"}
        await self.websocket_manager.broadcast_to_channel("broadcast_test", test_message)
        
        # Check that both connections received the message
        self.assertEqual(len(connection1.sent_messages), 1)
        self.assertEqual(len(connection2.sent_messages), 1)
        self.assertEqual(connection1.sent_messages[0], test_message)
        self.assertEqual(connection2.sent_messages[0], test_message)


class TestWebSocketBusService(WebSocketTestCase):
    """Test WebSocket bus service functionality."""
    
    def test_subscribe_unsubscribe(self):
        """Test bus service subscription."""
        self.bus_service.subscribe("conn_1", ["channel_1", "channel_2"])
        
        self.assertIn("conn_1", self.bus_service.channels["channel_1"])
        self.assertIn("conn_1", self.bus_service.channels["channel_2"])
        self.assertIn("channel_1", self.bus_service.connection_channels["conn_1"])
        self.assertIn("channel_2", self.bus_service.connection_channels["conn_1"])
        
        # Unsubscribe from one channel
        self.bus_service.unsubscribe("conn_1", ["channel_1"])
        
        self.assertNotIn("conn_1", self.bus_service.channels.get("channel_1", set()))
        self.assertIn("conn_1", self.bus_service.channels["channel_2"])
        
        # Unsubscribe from all channels
        self.bus_service.unsubscribe("conn_1")
        
        self.assertNotIn("conn_1", self.bus_service.connection_channels)
        
    async def test_broadcast_message(self):
        """Test broadcasting messages through bus service."""
        connection = self.create_mock_connection("test_1")
        
        # Subscribe to channel
        self.bus_service.subscribe("test_1", ["test_channel"])
        
        # Broadcast message
        test_message = {"type": "notification", "content": "test"}
        await self.bus_service.broadcast("test_channel", test_message)
        
        # Check message was received
        self.assertEqual(len(connection.sent_messages), 1)
        sent_message = connection.sent_messages[0]
        self.assertEqual(sent_message["type"], "bus_message")
        self.assertEqual(sent_message["channel"], "test_channel")
        self.assertIn("id", sent_message)
        self.assertIn("timestamp", sent_message)
        
    def test_message_queue(self):
        """Test message queuing for late subscribers."""
        # Broadcast message before subscription
        test_message = {"type": "notification", "content": "test"}
        asyncio.run(self.bus_service.broadcast("test_channel", test_message))
        
        # Get messages from queue
        messages = self.bus_service.get_messages("test_channel", 0)
        self.assertEqual(len(messages), 1)
        self.assertEqual(messages[0]["content"], "test")


class TestWebSocketControllers(WebSocketTestCase):
    """Test WebSocket controller functionality."""
    
    def test_websocket_route_decorator(self):
        """Test WebSocket route decorator."""
        
        class TestController(WebSocketController):
            @websocket_route('/test/route', auth='user')
            async def test_handler(self, connection):
                await connection.send_json({"message": "test"})
                
        controller = TestController()
        
        # Check that route was registered
        self.assertEqual(len(controller.routes), 1)
        route = controller.routes[0]
        self.assertEqual(route.pattern, '/test/route')
        self.assertEqual(route.auth, 'user')
        
    async def test_route_matching(self):
        """Test route pattern matching."""
        
        class TestController(WebSocketController):
            @websocket_route('/test/<string:param>', auth='public')
            async def test_handler(self, connection, param):
                await connection.send_json({"param": param})
                
        controller = TestController()
        connection = self.create_mock_connection()
        
        # Test route matching
        result = await controller.handle_connection(connection, '/test/hello')
        self.assertTrue(result)
        
        # Check that handler was called with correct parameter
        self.assertEqual(len(connection.sent_messages), 1)
        self.assertEqual(connection.sent_messages[0]["param"], "hello")


class WebSocketIntegrationTest(HttpCase):
    """Integration tests for WebSocket functionality."""
    
    def setUp(self):
        super().setUp()
        # These tests would require a running ASGI server
        # For now, we'll test the components in isolation
        
    def test_websocket_authentication(self):
        """Test WebSocket authentication flow."""
        # This would test the full authentication flow
        # with real session data
        pass
        
    def test_bus_integration(self):
        """Test integration with Odoo's bus.bus model."""
        # This would test the integration between WebSocket bus
        # and Odoo's existing bus system
        pass


class WebSocketPerformanceTest(WebSocketTestCase):
    """Performance tests for WebSocket functionality."""
    
    def test_many_connections(self):
        """Test handling many concurrent connections."""
        connections = []
        
        # Create many connections
        for i in range(100):
            connection = self.create_mock_connection(f"perf_test_{i}")
            connections.append(connection)
            
        self.assertEqual(len(self.websocket_manager.connections), 100)
        
        # Subscribe all to the same channel
        for i, connection in enumerate(connections):
            self.websocket_manager.subscribe_to_channel(f"perf_test_{i}", "performance_test")
            
        # Test broadcast performance
        import time
        start_time = time.time()
        
        asyncio.run(self.websocket_manager.broadcast_to_channel(
            "performance_test", 
            {"type": "performance_test", "data": "test"}
        ))
        
        end_time = time.time()
        broadcast_time = end_time - start_time
        
        # Should complete broadcast in reasonable time (< 1 second for 100 connections)
        self.assertLess(broadcast_time, 1.0)
        
        # Verify all connections received the message
        for connection in connections:
            self.assertEqual(len(connection.sent_messages), 1)


# Test utilities for external use
class WebSocketTestClient:
    """Test client for WebSocket connections."""
    
    def __init__(self, url: str, headers: Dict[str, str] = None):
        self.url = url
        self.headers = headers or {}
        self.messages = []
        self.connected = False
        
    async def connect(self):
        """Connect to WebSocket endpoint."""
        # This would implement actual WebSocket connection for testing
        self.connected = True
        
    async def send_json(self, data: Dict[str, Any]):
        """Send JSON message."""
        if not self.connected:
            raise RuntimeError("Not connected")
        # Implementation would send actual message
        
    async def receive_json(self) -> Dict[str, Any]:
        """Receive JSON message."""
        if not self.connected:
            raise RuntimeError("Not connected")
        # Implementation would receive actual message
        return {}
        
    async def close(self):
        """Close connection."""
        self.connected = False


def create_websocket_test_client(url: str, session_id: str = None, db: str = None) -> WebSocketTestClient:
    """Create a WebSocket test client with authentication."""
    headers = {}
    if session_id:
        headers['Cookie'] = f'session_id={session_id}'
        
    query_params = []
    if db:
        query_params.append(f'db={db}')
        
    if query_params:
        url += '?' + '&'.join(query_params)
        
    return WebSocketTestClient(url, headers)
