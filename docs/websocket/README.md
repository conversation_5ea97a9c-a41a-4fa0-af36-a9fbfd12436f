# WebSocket Support in Odoo ASGI

This document describes the WebSocket implementation in Odoo's ASGI architecture, providing real-time communication capabilities for modern web applications.

## Overview

The WebSocket implementation provides:

- **Modern ASGI-based WebSocket handling** using `wsproto` instead of legacy `websockets`
- **Authentication integration** with Odoo's session management
- **Routing system** similar to HTTP controllers
- **Bus service** for real-time notifications and messaging
- **Channel-based communication** for organized message distribution
- **Integration** with existing Odoo bus.bus system

## Architecture

### Core Components

1. **WebSocket Handler** (`odoo/http/websocket.py`)
   - Connection management
   - Authentication
   - Message routing

2. **WebSocket Controllers** (`odoo/http/websocket_controllers.py`)
   - Route-based message handling
   - Controller pattern similar to HTTP

3. **Bus Service** (`odoo/http/websocket_bus.py`)
   - Real-time messaging
   - Channel subscriptions
   - Integration with Odoo's bus system

4. **Default Controllers** (`odoo/http/default_websocket_controllers.py`)
   - Built-in endpoints for common use cases

## Configuration

### Uvicorn Configuration

The ASGI server is configured to use modern WebSocket protocols:

```python
config_dict = {
    'ws': 'wsproto',  # Modern WebSocket implementation
    'ws_max_size': ********,  # 16MB max message size
    'ws_ping_interval': 20.0,  # Ping interval in seconds
    'ws_ping_timeout': 20.0,   # Ping timeout in seconds
    'ws_per_message_deflate': True,  # Enable compression
}
```

### Odoo Configuration

WebSocket-related configuration options:

```ini
[options]
websocket_keep_alive_timeout = 3600  # Connection timeout in seconds
websocket_rate_limit_burst = 10      # Rate limiting burst size
websocket_rate_limit_delay = 0.2     # Rate limiting delay
```

## Available Endpoints

### Public Endpoints

#### `/ws/echo`
- **Authentication**: Public
- **Purpose**: Echo messages back to sender
- **Usage**: Testing and development

```javascript
const ws = new WebSocket('ws://localhost:8069/ws/echo');
ws.onopen = () => {
    ws.send(JSON.stringify({type: 'test', message: 'Hello!'}));
};
```

#### `/ws/status`
- **Authentication**: Public
- **Purpose**: Get server status information
- **Usage**: Health checks and monitoring

### Authenticated Endpoints

#### `/ws/bus`
- **Authentication**: User
- **Purpose**: Real-time bus messaging
- **Features**: Channel subscriptions, message broadcasting

```javascript
const ws = new WebSocket('ws://localhost:8069/ws/bus?session_id=SESSION_ID&db=DATABASE');
ws.onopen = () => {
    // Subscribe to channels
    ws.send(JSON.stringify({
        type: 'subscribe',
        channels: ['user_1', 'notifications']
    }));
};
```

#### `/ws/notifications`
- **Authentication**: User
- **Purpose**: User-specific notifications
- **Features**: Automatic channel subscription, notification management

#### `/ws/channel/<channel_name>`
- **Authentication**: User
- **Purpose**: Channel-based communication
- **Features**: Room-like messaging, broadcasting

#### `/ws/admin/stats`
- **Authentication**: Admin
- **Purpose**: Administrative statistics
- **Features**: Connection monitoring, channel statistics

## Authentication

WebSocket connections are authenticated using Odoo's session system:

### Session-based Authentication

1. **Query Parameters**:
   ```
   ws://localhost:8069/ws/endpoint?session_id=SESSION_ID&db=DATABASE
   ```

2. **Cookies**:
   ```javascript
   // Session ID from cookie is automatically used
   const ws = new WebSocket('ws://localhost:8069/ws/endpoint');
   ```

### Authentication Levels

- **`public`**: No authentication required
- **`user`**: Valid user session required
- **`admin`**: Admin privileges required

## Creating WebSocket Controllers

### Basic Controller

```python
from odoo.http.websocket_controllers import WebSocketController, websocket_route

class MyWebSocketController(WebSocketController):
    
    @websocket_route('/ws/my_endpoint', auth='user')
    async def my_handler(self, connection):
        await connection.send_json({
            'type': 'welcome',
            'message': 'Connected to my endpoint'
        })
        
        async for message in connection.messages():
            # Handle incoming messages
            await connection.send_json({
                'type': 'echo',
                'data': message
            })
```

### Parameterized Routes

```python
@websocket_route('/ws/room/<string:room_id>', auth='user')
async def room_handler(self, connection, room_id):
    # Subscribe to room channel
    websocket_manager.subscribe_to_channel(
        connection.connection_id, 
        f'room_{room_id}'
    )
    
    await connection.send_json({
        'type': 'joined_room',
        'room_id': room_id
    })
```

## Bus Service Usage

### Publishing Messages

```python
from odoo.http.websocket_bus import send_notification, send_broadcast

# Send to specific user
await send_notification(user_id=1, db_name='mydb', notification={
    'type': 'alert',
    'message': 'Important notification'
})

# Broadcast to all users in database
await send_broadcast(db_name='mydb', message={
    'type': 'announcement',
    'message': 'System maintenance in 10 minutes'
})
```

### Channel Subscriptions

```python
# Subscribe to channels
bus_service.subscribe(connection_id, ['user_1', 'notifications'])

# Broadcast to channel
await bus_service.broadcast('notifications', {
    'type': 'system_alert',
    'message': 'New system update available'
})
```

## Integration with Odoo Bus

The WebSocket bus automatically integrates with Odoo's existing `bus.bus` model:

```python
# Traditional bus.bus usage still works
self.env['bus.bus']._sendone('res.users,1', {
    'type': 'notification',
    'message': 'Hello from traditional bus'
})

# This message will be automatically forwarded to WebSocket subscribers
```

## Client-Side Usage

### JavaScript WebSocket Client

```javascript
class OdooWebSocketClient {
    constructor(endpoint, sessionId, database) {
        this.endpoint = endpoint;
        this.sessionId = sessionId;
        this.database = database;
        this.ws = null;
    }
    
    connect() {
        const url = `ws://localhost:8069${this.endpoint}?session_id=${this.sessionId}&db=${this.database}`;
        this.ws = new WebSocket(url);
        
        this.ws.onopen = () => console.log('Connected');
        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
        };
        this.ws.onclose = () => console.log('Disconnected');
    }
    
    send(data) {
        this.ws.send(JSON.stringify(data));
    }
    
    handleMessage(data) {
        console.log('Received:', data);
    }
}

// Usage
const client = new OdooWebSocketClient('/ws/notifications', 'session_123', 'mydb');
client.connect();
```

## Testing

### Unit Tests

```python
from odoo.tests.test_websocket import WebSocketTestCase

class TestMyWebSocket(WebSocketTestCase):
    
    def test_my_endpoint(self):
        connection = self.create_mock_connection()
        # Test your WebSocket functionality
```

### Integration Tests

Use the provided test client:

```python
from odoo.tests.test_websocket import create_websocket_test_client

async def test_websocket_integration():
    client = create_websocket_test_client('/ws/echo')
    await client.connect()
    await client.send_json({'test': 'message'})
    response = await client.receive_json()
    assert response['type'] == 'echo'
```

## Performance Considerations

### Connection Limits

- Monitor active connections using `/ws/admin/stats`
- Implement connection pooling for high-traffic scenarios
- Use channel-based messaging to reduce individual connections

### Message Size

- Default maximum message size: 16MB
- Use compression for large messages
- Consider pagination for large datasets

### Rate Limiting

- Built-in rate limiting based on Odoo configuration
- Implement application-level rate limiting for specific endpoints
- Monitor connection patterns and adjust limits accordingly

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Verify session ID is valid
   - Check database name matches session
   - Ensure user has required permissions

2. **Connection Drops**
   - Implement ping/pong heartbeat
   - Handle reconnection logic
   - Monitor network stability

3. **Message Delivery Issues**
   - Check channel subscriptions
   - Verify message format
   - Monitor server logs for errors

### Debugging

Enable WebSocket logging:

```python
import logging
logging.getLogger('odoo.http.websocket').setLevel(logging.DEBUG)
```

### Monitoring

Use the admin stats endpoint to monitor:
- Active connections
- Channel subscriptions
- Message throughput
- Error rates

## Migration from Longpolling

For applications currently using longpolling:

1. **Identify longpolling endpoints**
2. **Create equivalent WebSocket handlers**
3. **Update client-side code** to use WebSocket
4. **Test thoroughly** before switching
5. **Maintain backward compatibility** during transition

## Security Considerations

- **Authentication**: Always validate sessions
- **Authorization**: Check user permissions for channels
- **Rate Limiting**: Prevent abuse and DoS attacks
- **Message Validation**: Sanitize all incoming data
- **Channel Access**: Implement proper channel access controls

## Quick Reference

### WebSocket URLs

| Endpoint | Auth | Purpose |
|----------|------|---------|
| `/ws/echo` | Public | Echo messages |
| `/ws/status` | Public | Server status |
| `/ws/bus` | User | Bus messaging |
| `/ws/notifications` | User | User notifications |
| `/ws/channel/<name>` | User | Channel communication |
| `/ws/admin/stats` | Admin | Admin statistics |

### Message Types

| Type | Purpose | Example |
|------|---------|---------|
| `ping` | Keep-alive | `{"type": "ping"}` |
| `subscribe` | Join channels | `{"type": "subscribe", "channels": ["user_1"]}` |
| `broadcast` | Send to channel | `{"type": "broadcast", "data": {...}}` |
| `publish` | Publish message | `{"type": "publish", "channel": "test", "message": {...}}` |

### Authentication

```javascript
// With session ID
const ws = new WebSocket('ws://localhost:8069/ws/endpoint?session_id=SESSION&db=DB');

// With cookies (automatic)
const ws = new WebSocket('ws://localhost:8069/ws/endpoint');
```

### Python API

```python
# Send notification
await send_notification(user_id, db_name, {'message': 'Hello'})

# Broadcast message
await send_broadcast(db_name, {'announcement': 'System update'})

# Subscribe to channel
bus_service.subscribe(connection_id, ['channel_name'])
```

## Future Enhancements

- **Horizontal Scaling**: Redis-based message distribution
- **Message Persistence**: Store messages for offline users
- **Advanced Routing**: Pattern-based routing with middleware
- **Metrics**: Detailed performance and usage metrics
- **WebRTC Integration**: Real-time audio/video communication
