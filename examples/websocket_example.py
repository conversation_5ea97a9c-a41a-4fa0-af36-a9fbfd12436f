#!/usr/bin/env python3
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
WebSocket Example for Odoo ASGI Application.

This script demonstrates how to connect to and use Odoo's WebSocket endpoints
for real-time communication.
"""

import asyncio
import json
import logging
import websockets
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OdooWebSocketClient:
    """WebSocket client for connecting to Odoo."""
    
    def __init__(self, url: str, session_id: str = None, db: str = None):
        self.url = url
        self.session_id = session_id
        self.db = db
        self.websocket = None
        self.connected = False
        
    async def connect(self):
        """Connect to the WebSocket endpoint."""
        # Build URL with authentication parameters
        connect_url = self.url
        params = []
        
        if self.session_id:
            params.append(f'session_id={self.session_id}')
        if self.db:
            params.append(f'db={self.db}')
            
        if params:
            connect_url += '?' + '&'.join(params)
            
        try:
            self.websocket = await websockets.connect(connect_url)
            self.connected = True
            logger.info(f"Connected to {connect_url}")
        except Exception as e:
            logger.error(f"Failed to connect: {e}")
            raise
            
    async def disconnect(self):
        """Disconnect from the WebSocket."""
        if self.websocket:
            await self.websocket.close()
            self.connected = False
            logger.info("Disconnected from WebSocket")
            
    async def send_json(self, data: Dict[str, Any]):
        """Send JSON message to the WebSocket."""
        if not self.connected:
            raise RuntimeError("Not connected to WebSocket")
            
        message = json.dumps(data)
        await self.websocket.send(message)
        logger.debug(f"Sent: {message}")
        
    async def receive_json(self) -> Dict[str, Any]:
        """Receive JSON message from the WebSocket."""
        if not self.connected:
            raise RuntimeError("Not connected to WebSocket")
            
        message = await self.websocket.recv()
        data = json.loads(message)
        logger.debug(f"Received: {message}")
        return data
        
    async def listen(self, message_handler=None):
        """Listen for messages from the WebSocket."""
        try:
            while self.connected:
                data = await self.receive_json()
                if message_handler:
                    await message_handler(data)
                else:
                    logger.info(f"Received message: {data}")
        except websockets.exceptions.ConnectionClosed:
            logger.info("WebSocket connection closed")
            self.connected = False
        except Exception as e:
            logger.error(f"Error listening for messages: {e}")


async def echo_example():
    """Example using the echo WebSocket endpoint."""
    client = OdooWebSocketClient('ws://localhost:8069/ws/echo')
    
    try:
        await client.connect()
        
        # Send some test messages
        await client.send_json({'type': 'test', 'message': 'Hello WebSocket!'})
        response = await client.receive_json()
        print(f"Echo response: {response}")
        
        # Send ping
        await client.send_json({'type': 'ping'})
        pong = await client.receive_json()
        print(f"Ping response: {pong}")
        
    finally:
        await client.disconnect()


async def bus_example():
    """Example using the bus WebSocket endpoint."""
    # Note: This requires authentication
    client = OdooWebSocketClient(
        'ws://localhost:8069/ws/bus',
        session_id='your_session_id_here',  # Replace with actual session ID
        db='your_database_name'  # Replace with actual database name
    )
    
    try:
        await client.connect()
        
        # Subscribe to channels
        await client.send_json({
            'type': 'subscribe',
            'channels': ['user_1', 'public_notifications']
        })
        
        response = await client.receive_json()
        print(f"Subscription response: {response}")
        
        # Listen for messages for 10 seconds
        async def message_handler(data):
            print(f"Bus message: {data}")
            
        # Set up a timeout for the example
        try:
            await asyncio.wait_for(client.listen(message_handler), timeout=10.0)
        except asyncio.TimeoutError:
            print("Listening timeout reached")
            
    finally:
        await client.disconnect()


async def channel_example():
    """Example using channel-based WebSocket endpoint."""
    client = OdooWebSocketClient(
        'ws://localhost:8069/ws/channel/test_room',
        session_id='your_session_id_here',  # Replace with actual session ID
        db='your_database_name'  # Replace with actual database name
    )
    
    try:
        await client.connect()
        
        # Wait for connection confirmation
        response = await client.receive_json()
        print(f"Channel join response: {response}")
        
        # Send a broadcast message
        await client.send_json({
            'type': 'broadcast',
            'data': {
                'message': 'Hello everyone in the channel!',
                'sender': 'example_client'
            }
        })
        
        # Listen for responses
        for _ in range(3):  # Listen for a few messages
            try:
                response = await asyncio.wait_for(client.receive_json(), timeout=2.0)
                print(f"Channel message: {response}")
            except asyncio.TimeoutError:
                break
                
    finally:
        await client.disconnect()


async def notifications_example():
    """Example using notifications WebSocket endpoint."""
    client = OdooWebSocketClient(
        'ws://localhost:8069/ws/notifications',
        session_id='your_session_id_here',  # Replace with actual session ID
        db='your_database_name'  # Replace with actual database name
    )
    
    try:
        await client.connect()
        
        # Wait for ready message
        response = await client.receive_json()
        print(f"Notifications ready: {response}")
        
        # Subscribe to additional channels
        await client.send_json({
            'type': 'subscribe',
            'channel': 'custom_notifications'
        })
        
        # Listen for notifications
        async def notification_handler(data):
            print(f"Notification: {data}")
            
            # Mark notifications as read
            if data.get('type') == 'notification' and 'notification_id' in data:
                await client.send_json({
                    'type': 'mark_read',
                    'notification_id': data['notification_id']
                })
                
        # Listen for 15 seconds
        try:
            await asyncio.wait_for(client.listen(notification_handler), timeout=15.0)
        except asyncio.TimeoutError:
            print("Notification listening timeout reached")
            
    finally:
        await client.disconnect()


async def status_example():
    """Example using the status WebSocket endpoint."""
    client = OdooWebSocketClient('ws://localhost:8069/ws/status')
    
    try:
        await client.connect()
        
        # The status endpoint sends status and closes
        response = await client.receive_json()
        print(f"Server status: {response}")
        
    finally:
        await client.disconnect()


async def main():
    """Run WebSocket examples."""
    print("Odoo WebSocket Examples")
    print("======================")
    
    examples = [
        ("Echo Example", echo_example),
        ("Status Example", status_example),
        # Uncomment these when you have valid session credentials
        # ("Bus Example", bus_example),
        # ("Channel Example", channel_example),
        # ("Notifications Example", notifications_example),
    ]
    
    for name, example_func in examples:
        print(f"\n--- {name} ---")
        try:
            await example_func()
        except Exception as e:
            print(f"Example failed: {e}")
            
    print("\nAll examples completed!")


if __name__ == '__main__':
    # Install websockets library: pip install websockets
    try:
        import websockets
    except ImportError:
        print("Please install websockets library: pip install websockets")
        exit(1)
        
    asyncio.run(main())
